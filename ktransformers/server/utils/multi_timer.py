import time


def format_time(seconds):
    units = [
        ("hours", 3600),
        ("minutes", 60),
        ("seconds", 1),
        ("milliseconds", 1e-3),
        ("microseconds", 1e-6),
    ]

    for unit_name, unit_value in units:
        if seconds >= unit_value:
            time_value = seconds / unit_value
            return f"{time_value:.2f} {unit_name}"
    return "0 seconds"  # Handle case for 0 seconds


class Profiler:
    def __init__(self):
        self.timers = {}
        self.counters = {}

    def create_timer(self, name):
        self.timers[name] = {
            "start_time": None,
            "elapsed_time": 0,
            "running": False,
        }

    def start_timer(self, name):
        if name not in self.timers:
            raise ValueError(f"Timer '{name}' does not exist.")
        if self.timers[name]["running"]:
            raise ValueError(f"Timer '{name}' is already running.")
        self.timers[name]["start_time"] = time.time()
        self.timers[name]["running"] = True

    def pause_timer(self, name):
        if name not in self.timers:
            raise ValueError(f"Timer '{name}' does not exist.")
        if not self.timers[name]["running"]:
            raise ValueError(f"Timer '{name}' is not running.")
        self.timers[name]["elapsed_time"] += time.time() - self.timers[name]["start_time"]
        self.timers[name]["running"] = False

    def get_timer_sec(self, name):
        if name not in self.timers:
            raise ValueError(f"Timer '{name}' does not exist.")
        if self.timers[name]["running"]:
            current_time = self.timers[name]["elapsed_time"] + (time.time() - self.timers[name]["start_time"])
        else:
            current_time = self.timers[name]["elapsed_time"]
        return current_time

    def get_all_timers(self):
        all_timers = {}
        for name in self.timers:
            all_timers[name] = self.get_timer_sec(name)
        return all_timers

    def report_timer_string(self, name):
        return f"{name} elapsed time: {format_time(self.get_timer_sec(name))}"

    def create_and_start_timer(self, name):
        self.create_timer(name)
        self.start_timer(name)


    # Counter
    def inc(self,key:str,delta:int=1):
        self.counters[key] = self.counters.get(key,0) + delta

    def set_counter(self,key:str,to=0):
        self.counters[key] = to

    def get_counter(self,key:str):
        return self.counters.get(key,0)

    # CUDA事件计时器方法
    def create_cuda_timer(self, name: str):
        """创建CUDA事件计时器"""
        if torch.cuda.is_available():
            start_event = torch.cuda.Event(enable_timing=True)
            end_event = torch.cuda.Event(enable_timing=True)
            self.cuda_events[name] = {
                "start": start_event,
                "end": end_event,
                "elapsed": 0.0,
                "running": False
            }

    def start_cuda_timer(self, name: str):
        """启动CUDA计时器"""
        if name in self.cuda_events and torch.cuda.is_available():
            self.cuda_events[name]["start"].record()
            self.cuda_events[name]["running"] = True

    def stop_cuda_timer(self, name: str):
        """停止CUDA计时器"""
        if name in self.cuda_events and torch.cuda.is_available():
            self.cuda_events[name]["end"].record()
            torch.cuda.synchronize()
            elapsed = self.cuda_events[name]["start"].elapsed_time(self.cuda_events[name]["end"])
            self.cuda_events[name]["elapsed"] = elapsed / 1000.0  # 转换为秒
            self.cuda_events[name]["running"] = False

    def get_cuda_timer_sec(self, name: str) -> float:
        """获取CUDA计时器时间（秒）"""
        if name in self.cuda_events:
            return self.cuda_events[name]["elapsed"]
        return 0.0

    # 性能快照方法
    def take_performance_snapshot(self):
        """获取当前性能快照"""
        snapshot = PerformanceSnapshot(
            timestamp=time.time(),
            cpu_percent=self.process.cpu_percent(),
            memory_percent=self.process.memory_percent(),
            memory_used_mb=self.process.memory_info().rss / 1024 / 1024
        )

        # 获取GPU信息
        if torch.cuda.is_available():
            try:
                snapshot.gpu_memory_used_mb = torch.cuda.memory_allocated() / 1024 / 1024
                # 注意：GPU利用率需要nvidia-ml-py库
                # snapshot.gpu_utilization = get_gpu_utilization()
            except:
                pass

        self.performance_snapshots.append(snapshot)
        return snapshot

    # 专家模块性能监控方法
    def start_expert_monitoring(self, expert_key: str, device: str, operation: str):
        """开始专家模块性能监控"""
        self.create_timer(f"expert_{expert_key}")
        self.start_timer(f"expert_{expert_key}")

        if device == "cuda":
            self.create_cuda_timer(f"expert_cuda_{expert_key}")
            self.start_cuda_timer(f"expert_cuda_{expert_key}")

        # 记录开始时的性能快照
        self.take_performance_snapshot()

        # 初始化专家指标
        self.expert_metrics[expert_key] = ExpertMetrics(
            device=device,
            operation=operation,
            latency=0.0,
            throughput=0.0,
            memory_used=0.0,
            memory_peak=0.0,
            cpu_utilization=0.0,
            gpu_utilization=None
        )

    def stop_expert_monitoring(self, expert_key: str, token_count: int, expert_count: int = 1):
        """停止专家模块性能监控"""
        # 停止计时器
        self.pause_timer(f"expert_{expert_key}")
        latency = self.get_timer_sec(f"expert_{expert_key}")

        if f"expert_cuda_{expert_key}" in self.cuda_events:
            self.stop_cuda_timer(f"expert_cuda_{expert_key}")
            cuda_latency = self.get_cuda_timer_sec(f"expert_cuda_{expert_key}")
        else:
            cuda_latency = latency

        # 获取结束时的性能快照
        end_snapshot = self.take_performance_snapshot()

        # 计算性能指标
        if expert_key in self.expert_metrics:
            metrics = self.expert_metrics[expert_key]
            metrics.latency = cuda_latency if metrics.device == "cuda" else latency
            metrics.throughput = token_count / metrics.latency if metrics.latency > 0 else 0
            metrics.token_count = token_count
            metrics.expert_count = expert_count
            metrics.memory_used = end_snapshot.memory_used_mb
            metrics.cpu_utilization = end_snapshot.cpu_percent

            if metrics.device == "cuda" and end_snapshot.gpu_memory_used_mb:
                metrics.gpu_utilization = end_snapshot.gpu_utilization
                metrics.memory_used = end_snapshot.gpu_memory_used_mb

    def get_expert_metrics(self, expert_key: str) -> Optional[ExpertMetrics]:
        """获取专家模块性能指标"""
        return self.expert_metrics.get(expert_key)

    def get_all_expert_metrics(self) -> Dict[str, ExpertMetrics]:
        """获取所有专家模块性能指标"""
        return self.expert_metrics.copy()

    def get_cuda_vs_cpu_comparison(self) -> Dict[str, Any]:
        """获取CUDA vs CPU性能对比"""
        cuda_metrics = []
        cpu_metrics = []

        for key, metrics in self.expert_metrics.items():
            if metrics.device == "cuda":
                cuda_metrics.append(metrics)
            else:
                cpu_metrics.append(metrics)

        def calculate_stats(metrics_list):
            if not metrics_list:
                return {}

            latencies = [m.latency for m in metrics_list]
            throughputs = [m.throughput for m in metrics_list]
            memory_usage = [m.memory_used for m in metrics_list]

            return {
                "count": len(metrics_list),
                "avg_latency": sum(latencies) / len(latencies),
                "avg_throughput": sum(throughputs) / len(throughputs),
                "avg_memory": sum(memory_usage) / len(memory_usage),
                "total_tokens": sum(m.token_count for m in metrics_list),
                "total_experts": sum(m.expert_count for m in metrics_list)
            }

        cuda_stats = calculate_stats(cuda_metrics)
        cpu_stats = calculate_stats(cpu_metrics)

        comparison = {
            "cuda": cuda_stats,
            "cpu": cpu_stats,
            "comparison": {}
        }

        # 计算对比指标
        if cuda_stats and cpu_stats:
            comparison["comparison"] = {
                "latency_ratio": cuda_stats["avg_latency"] / cpu_stats["avg_latency"] if cpu_stats["avg_latency"] > 0 else 0,
                "throughput_ratio": cuda_stats["avg_throughput"] / cpu_stats["avg_throughput"] if cpu_stats["avg_throughput"] > 0 else 0,
                "memory_ratio": cuda_stats["avg_memory"] / cpu_stats["avg_memory"] if cpu_stats["avg_memory"] > 0 else 0
            }

        return comparison

    def export_metrics_to_dict(self) -> Dict[str, Any]:
        """导出所有性能指标为字典"""
        return {
            "timers": {name: self.get_timer_sec(name) for name in self.timers.keys()},
            "counters": self.counters.copy(),
            "expert_metrics": {key: asdict(metrics) for key, metrics in self.expert_metrics.items()},
            "cuda_vs_cpu_comparison": self.get_cuda_vs_cpu_comparison(),
            "performance_snapshots": [asdict(snapshot) for snapshot in self.performance_snapshots[-10:]]  # 最近10个快照
        }
